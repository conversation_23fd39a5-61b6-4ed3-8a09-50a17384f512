// Load environment variables
require('dotenv').config();

const { Sequelize } = require('sequelize');

// Database configuration using environment variables
const sequelize = new Sequelize(
    process.env.DB_DATABASE,
    process.env.DB_USERNAME,
    process.env.DB_PASSWORD,
    {
        host: process.env.DB_HOST,
        port: process.env.DB_PORT,
        dialect: process.env.DB_CONNECTION,

        // Connection pool configuration
        pool: {
            min: parseInt(process.env.DB_POOL_MIN) || 0,
            max: parseInt(process.env.DB_POOL_MAX) || 5,
            acquire: parseInt(process.env.DB_POOL_ACQUIRE) || 30000,
            idle: parseInt(process.env.DB_POOL_IDLE) || 10000,
        },

        // Logging configuration
        logging: process.env.DB_LOGGING === 'true' ? console.log : false,

        // Timezone configuration
        timezone: process.env.DB_TIMEZONE || '+00:00',

        // SSL configuration
        dialectOptions: {
            ssl: process.env.DB_SSL === 'true' ? {
                require: true,
                rejectUnauthorized: false
            } : false,
        },

        // Additional options
        define: {
            timestamps: true,
            underscored: true,
            freezeTableName: true,
        },

        // Retry configuration
        retry: {
            max: 3,
        },
    }
);

module.exports = sequelize;