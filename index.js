// Load environment variables
require('dotenv').config();

const express = require('express');
const sequelize = require('./config/config');
const userRoutes = require('./routes/user.route');
const cors = require('cors');

const app = express();
app.use(express.json());
app.use(cors());

// Routes
app.use('/api/users', userRoutes);

// Test DB Connection & Sync Models
sequelize.authenticate()
    .then(() => {
        console.log('Database connected...');
        return sequelize.sync(); // Use { force: true } to reset tables
    })
    .then(() => {
        const PORT = process.env.PORT || 3000;
        app.listen(PORT, () => {
            console.log(`Server is running on port ${PORT}`);
        });
    })
    .catch(err => console.error('Database connection failed:', err));