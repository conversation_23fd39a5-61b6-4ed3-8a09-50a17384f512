# Database Configuration Guide

This document explains how to configure the database connection using environment variables.

## Environment Variables

The application uses the following environment variables for database configuration:

### Core Database Settings
- `DB_CONNECTION`: Database dialect (mysql, postgres, sqlite, mariadb)
- `DB_HOST`: Database host (default: localhost)
- `DB_PORT`: Database port (default: 3306 for MySQL)
- `DB_DATABASE`: Database name
- `DB_USERNAME`: Database username
- `DB_PASSWORD`: Database password

### Connection Pool Settings
- `DB_POOL_MIN`: Minimum number of connections in pool (default: 0)
- `DB_POOL_MAX`: Maximum number of connections in pool (default: 5)
- `DB_POOL_ACQUIRE`: Maximum time to get connection (default: 30000ms)
- `DB_POOL_IDLE`: Maximum time connection can be idle (default: 10000ms)

### Additional Options
- `DB_TIMEZONE`: Database timezone (default: +00:00)
- `DB_LOGGING`: Enable SQL query logging (true/false)
- `DB_SSL`: Enable SSL connection (true/false)

### Application Settings
- `NODE_ENV`: Environment (development, production, test)
- `PORT`: Server port (default: 3000)

## Setup Instructions

1. Copy `.env.example` to `.env`:
   ```bash
   cp .env.example .env
   ```

2. Update the `.env` file with your database credentials:
   ```env
   DB_CONNECTION=mysql
   DB_HOST=localhost
   DB_PORT=3306
   DB_DATABASE=your_database_name
   DB_USERNAME=your_username
   DB_PASSWORD=your_password
   ```

3. For production, make sure to:
   - Set `NODE_ENV=production`
   - Use strong passwords
   - Enable SSL if required (`DB_SSL=true`)
   - Adjust pool settings based on your needs
   - Set `DB_LOGGING=false` for better performance

## Database Support

The application supports the following databases:
- MySQL (default)
- PostgreSQL
- MariaDB
- SQLite

To switch databases, change the `DB_CONNECTION` value and install the appropriate driver:

### PostgreSQL
```bash
npm install pg pg-hstore
```
Set `DB_CONNECTION=postgres`

### SQLite
```bash
npm install sqlite3
```
Set `DB_CONNECTION=sqlite`

### MariaDB
```bash
npm install mariadb
```
Set `DB_CONNECTION=mariadb`

## Security Notes

- Never commit the `.env` file to version control
- Use strong, unique passwords
- Enable SSL in production environments
- Regularly rotate database credentials
- Use environment-specific configurations

## Troubleshooting

### Connection Issues
1. Verify database server is running
2. Check host and port settings
3. Verify credentials are correct
4. Check firewall settings
5. Ensure database exists

### Pool Issues
- Increase `DB_POOL_MAX` for high-traffic applications
- Adjust `DB_POOL_ACQUIRE` if getting timeout errors
- Monitor connection usage in production
